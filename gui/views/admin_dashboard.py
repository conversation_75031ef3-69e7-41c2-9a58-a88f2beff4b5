"""
Admin dashboard view for managing teachers and viewing system statistics.
"""
import flet as ft
from gui.services.auth_service import AuthService
from gui.components.layout import create_app_bar
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS

def create_admin_dashboard_view(page: ft.Page):
    """Create the admin dashboard with statistics and teacher management."""
    auth_service = AuthService()
    
    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])
    
    # Get statistics
    stats = auth_service.get_admin_statistics()
    
    def logout_click(e):
        """Handle logout."""
        page.session.clear()
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)
    
    def manage_teachers_click(e):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)
    
    # Statistics cards
    def create_stat_card(title: str, value: int, icon: str, color: str):
        """Create a statistics card."""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(icon, size=40, color=color),
                    ft.Column([
                        ft.Text(str(value), size=32, weight=ft.FontWeight.BOLD, color=color),
                        ft.Text(title, size=14, color=ft.Colors.GREY_600),
                    ], spacing=0, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ], spacing=10),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=15,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.Colors.GREY_200,
                offset=ft.Offset(0, 2)
            ),
            width=250,
            height=120,
        )
    
    stats_row = ft.Row([
        create_stat_card("Active Teachers", stats.get('active_teachers', 0), ft.Icons.PERSON, ft.Colors.BLUE_600),
        create_stat_card("Total Classes", stats.get('total_classes', 0), ft.Icons.CLASS_, ft.Colors.GREEN_600),
        create_stat_card("Total Students", stats.get('total_students', 0), ft.Icons.PEOPLE, ft.Colors.ORANGE_600),
        create_stat_card("Total Subjects", stats.get('total_subjects', 0), ft.Icons.BOOK, ft.Colors.PURPLE_600),
    ], spacing=20, wrap=True)
    
    stats_row2 = ft.Row([
        create_stat_card("Total Quizzes", stats.get('total_quizzes', 0), ft.Icons.QUIZ, ft.Colors.RED_600),
        create_stat_card("Recent Attendance", stats.get('recent_attendance', 0), ft.Icons.FACT_CHECK, ft.Colors.TEAL_600),
    ], spacing=20, wrap=True)
    
    # Action buttons
    manage_teachers_btn = ft.ElevatedButton(
        text="Manage Teachers",
        icon=ft.Icons.PERSON_ADD,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=30, vertical=15),
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_500),
            shape=ft.RoundedRectangleBorder(radius=10),
        ),
        on_click=manage_teachers_click
    )
    
    view_data_btn = ft.ElevatedButton(
        text="View All Data",
        icon=ft.Icons.ANALYTICS,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.GREEN_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=30, vertical=15),
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_500),
            shape=ft.RoundedRectangleBorder(radius=10),
        ),
        on_click=lambda e: page.go("/admin/data")
    )
    
    # Welcome section
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                f"Welcome, {current_user.get('full_name', 'Admin')}",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE_900
            ),
            ft.Text(
                "Administrator Dashboard",
                size=16,
                color=ft.Colors.GREY_600
            ),
        ], spacing=5),
        margin=ft.margin.only(bottom=30)
    )
    
    # Quick actions section
    actions_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "Quick Actions",
                size=20,
                weight=ft.FontWeight.W_600,
                color=ft.Colors.BLUE_900
            ),
            ft.Container(height=15),
            ft.Row([
                manage_teachers_btn,
                view_data_btn,
            ], spacing=20)
        ], spacing=0),
        margin=ft.margin.only(top=30, bottom=20)
    )
    
    # Main content
    main_content = ft.Container(
        content=ft.Column([
            welcome_section,
            ft.Text(
                "System Overview",
                size=20,
                weight=ft.FontWeight.W_600,
                color=ft.Colors.BLUE_900
            ),
            ft.Container(height=15),
            stats_row,
            ft.Container(height=20),
            stats_row2,
            actions_section,
        ], spacing=0),
        padding=ft.padding.all(30),
        expand=True
    )
    
    # App bar with logout
    app_bar = ft.AppBar(
        title=ft.Text("Admin Dashboard", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
        bgcolor=ft.Colors.BLUE_600,
        actions=[
            ft.IconButton(
                icon=ft.Icons.LOGOUT,
                icon_color=ft.Colors.WHITE,
                tooltip="Logout",
                on_click=logout_click
            )
        ]
    )
    
    # Create the view
    view = ft.View(
        route="/admin",
        controls=[main_content],
        appbar=app_bar,
        padding=0,
        spacing=0,
        bgcolor=ft.Colors.GREY_50
    )
    
    return view
