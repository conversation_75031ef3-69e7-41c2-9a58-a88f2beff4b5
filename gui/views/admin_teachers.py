"""
Teacher management view for admin.
"""
import flet as ft
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD

def create_admin_teachers_view(page: ft.Page):
    """Create the teacher management view."""
    auth_service = AuthService()
    
    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/teachers", controls=[])
    
    # State variables
    teachers_list = ft.Column(spacing=10)
    
    def refresh_teachers():
        """Refresh the teachers list."""
        teachers_list.controls.clear()
        teachers = auth_service.get_all_teachers()
        
        if not teachers:
            teachers_list.controls.append(
                ft.Container(
                    content=ft.Text(
                        "No teachers found. Add your first teacher!",
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    alignment=ft.alignment.center,
                    padding=ft.padding.all(40)
                )
            )
        else:
            for teacher in teachers:
                teachers_list.controls.append(create_teacher_card(teacher))
        
        page.update()
    
    def create_teacher_card(teacher: dict):
        """Create a teacher card."""
        status_color = ft.Colors.GREEN_600 if teacher['is_active'] else ft.Colors.RED_600
        status_text = "Active" if teacher['is_active'] else "Inactive"
        
        def toggle_status(e):
            """Toggle teacher active status."""
            new_status = not teacher['is_active']
            if auth_service.update_teacher_status(teacher['id'], new_status):
                refresh_teachers()
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text(f"Teacher status updated to {'Active' if new_status else 'Inactive'}"))
                )
        
        def delete_teacher(e):
            """Delete teacher after confirmation."""
            def confirm_delete(e):
                if auth_service.delete_teacher(teacher['id']):
                    refresh_teachers()
                    page.show_snack_bar(
                        ft.SnackBar(content=ft.Text("Teacher deleted successfully"))
                    )
                page.dialog.open = False
                page.update()
            
            def cancel_delete(e):
                page.dialog.open = False
                page.update()
            
            page.dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Confirm Delete"),
                content=ft.Text(f"Are you sure you want to delete teacher '{teacher['full_name']}'?"),
                actions=[
                    ft.TextButton("Cancel", on_click=cancel_delete),
                    ft.TextButton("Delete", on_click=confirm_delete, style=ft.ButtonStyle(color=ft.Colors.RED)),
                ]
            )
            page.dialog.open = True
            page.update()
        
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(teacher['full_name'], size=16, weight=ft.FontWeight.W_600),
                    ft.Text(f"Username: {teacher['username']}", size=14, color=ft.Colors.GREY_600),
                    ft.Text(f"Email: {teacher['email'] or 'Not provided'}", size=14, color=ft.Colors.GREY_600),
                    ft.Text(f"Created: {teacher['created_at'][:10]}", size=12, color=ft.Colors.GREY_500),
                ], spacing=2, expand=True),
                ft.Column([
                    ft.Container(
                        content=ft.Text(status_text, color=ft.Colors.WHITE, size=12, weight=ft.FontWeight.W_500),
                        bgcolor=status_color,
                        padding=ft.padding.symmetric(horizontal=12, vertical=4),
                        border_radius=12,
                    ),
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.TOGGLE_ON if teacher['is_active'] else ft.Icons.TOGGLE_OFF,
                            icon_color=status_color,
                            tooltip="Toggle Status",
                            on_click=toggle_status
                        ),
                        ft.IconButton(
                            icon=ft.Icons.DELETE,
                            icon_color=ft.Colors.RED_600,
                            tooltip="Delete Teacher",
                            on_click=delete_teacher
                        ),
                    ], spacing=0)
                ], horizontal_alignment=ft.CrossAxisAlignment.END, spacing=5)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=10,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=5,
                color=ft.Colors.GREY_200,
                offset=ft.Offset(0, 2)
            ),
        )
    
    def show_add_teacher_dialog(e):
        """Show add teacher dialog."""
        username_field = ft.TextField(label="Username", width=300)
        password_field = ft.TextField(label="Password", password=True, width=300)
        full_name_field = ft.TextField(label="Full Name", width=300)
        email_field = ft.TextField(label="Email (optional)", width=300)
        phone_field = ft.TextField(label="Phone (optional)", width=300)
        
        def add_teacher(e):
            """Add new teacher."""
            if not all([username_field.value, password_field.value, full_name_field.value]):
                print("Please fill in all required fields")
                return
            
            success = auth_service.create_teacher(
                username=username_field.value.strip(),
                password=password_field.value.strip(),
                full_name=full_name_field.value.strip(),
                email=email_field.value.strip() or None,
                phone=phone_field.value.strip() or None
            )
            
            if success:
                page.dialog.open = False
                refresh_teachers()
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text("Teacher created successfully"))
                )
            else:
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text("Failed to create teacher. Username might already exist."))
                )
            page.update()
        
        def cancel_add(e):
            dialog.open = False
            page.update()
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Add New Teacher"),
            content=ft.Column([
                username_field,
                password_field,
                full_name_field,
                email_field,
                phone_field,
            ], spacing=15, tight=True, height=300),
            actions=[
                ft.TextButton("Cancel", on_click=cancel_add),
                ft.ElevatedButton("Add Teacher", on_click=add_teacher),
            ]
        )
        page.add(dialog)
        dialog.open = True
        page.update()
    
    def back_to_dashboard(e):
        """Navigate back to admin dashboard."""
        page.go(ROUTE_ADMIN_DASHBOARD)
    
    def logout_click(e):
        """Handle logout."""
        page.session.clear()
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)
    
    # Header section
    header_section = ft.Container(
        content=ft.Row([
            ft.Column([
                ft.Text(
                    "Teacher Management",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                ),
                ft.Text(
                    "Manage teacher accounts and permissions",
                    size=14,
                    color=ft.Colors.GREY_600
                ),
            ], spacing=5),
            ft.ElevatedButton(
                text="Add Teacher",
                icon=ft.Icons.PERSON_ADD,
                style=ft.ButtonStyle(
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    padding=ft.padding.symmetric(horizontal=20, vertical=12),
                ),
                on_click=show_add_teacher_dialog
            )
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        margin=ft.margin.only(bottom=30)
    )
    
    # Main content
    main_content = ft.Container(
        content=ft.Column([
            header_section,
            ft.Container(
                content=teachers_list,
                expand=True
            )
        ], spacing=0),
        padding=ft.padding.all(30),
        expand=True
    )
    
    # App bar
    app_bar = ft.AppBar(
        title=ft.Text("Teacher Management", color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
        bgcolor=ft.Colors.BLUE_600,
        leading=ft.IconButton(
            icon=ft.Icons.ARROW_BACK,
            icon_color=ft.Colors.WHITE,
            on_click=back_to_dashboard
        ),
        actions=[
            ft.IconButton(
                icon=ft.Icons.LOGOUT,
                icon_color=ft.Colors.WHITE,
                tooltip="Logout",
                on_click=logout_click
            )
        ]
    )
    
    # Initialize teachers list
    refresh_teachers()
    
    # Create the view
    view = ft.View(
        route="/admin/teachers",
        controls=[main_content],
        appbar=app_bar,
        padding=0,
        spacing=0,
        bgcolor=ft.Colors.GREY_50
    )
    
    return view
